# -- coding: utf-8 --
# @Time : 2025-07-21 10:08:11
# <AUTHOR> <EMAIL>
import sys
import KatyushaDriver

args = sys.argv
driver = KatyushaDriver.Katyusha(args)
driver.stage('1、另存样张“合并表_合并规则”；', '1、另存样张“合并表_合并规则”；')
driver.execute_action(
    action_name='LaunchBrowser',
    desc='打开浏览器',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserType': 'Chrome',
            'IsSetMax': True,
            'BrowserName': '',
            'ConnectionOptions': '',
            'ExecutablePath': '',
        },
    },
)

driver.execute_action(
    action_name='AddCookies',
    desc='添加Cookie',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0', 
            'Cookies': [{'name': 'weboffice_branch', 'value': 'kdocs-amd-master-kso-v12', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}]
            },
    },
)
driver.execute_action(
    action_name='GoTo',
    desc='打开页面',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0',
            'Url': 'https://www.kdocs.cn/l/cqJesfCPEu72?R=L1MvMQ==',
            'WaitUntil': 'load',
            'Referer': 'none',
        },
    },
)
driver.execute_action(
    action_name='WaitTimeout',
    desc='延时等待',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'Duration': 5}},
)
driver.stage(
    '2、新建文件，点击新建-合并数据表-从其他文件选择的数据表-选择样张；',
    '2、新建文件，点击新建-合并数据表-从其他文件选择的数据表-选择样张；',
)
driver.execute_action(
    action_name='NewPage',
    desc='创建页面',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {'BrowserSelector': '0.0', 'PageName': ''},
    },
)
driver.execute_action(
    action_name='CreateWoDoc',
    desc='创建云文档',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 120,
        'Descr': '',
        'Arg': {
            'Component': 'dbsheet',
            'GroupId': '2000134968',
            'ParentId': '100000386746',
            'Cookies': [{'name': 'weboffice_branch', 'value': 'kdocs-amd-t-release-minio', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}],
            'FileName': 'DB-HBB-00000011',
        },
    },
)
driver.execute_action(
    action_name='AddCookies',
    desc='添加Cookie',
    app_type='WO_PC_V2',
    is_must_execute=True,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0', 
            'Cookies': [{'name': 'weboffice_branch', 'value': 'kdocs-amd-master-kso-v12', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}]
            },
    },
)
driver.execute_action(
    action_name='GoTo',
    desc='打开页面',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'Url': '%{5}.{link_url}%',
            'WaitUntil': 'load',
            'Referer': 'none',
        },
    },
)
driver.execute_action(
    action_name='WaitTimeout',
    desc='延时等待',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'Duration': 10}},
)
driver.execute_action(
    action_name='EvaluateJS',
    desc='点击新建-合并数据表，勾选数据表“不合并”，“不合并1”，点击合并',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'window.APP.execCommandPromise("dbSheet.addSyncMergeSheet", {     "srcSheetsInfoVec": [         {             "fileId": "100188412578",             "srcSheets": [                 {                     "sheetId": 1,                     "recordsCount": 6,                     "fields": [                         {                             "arraySupport": false,                             "autoFillSourceField": "",                             "customConfig": "",                             "defaultValueType": "Normal",                             "description": "",                             "id": "W",                             "name": "数字",                             "numberFormat": "0_ ",                             "syncField": false,                             "type": "Number"                         },                         {                             "arraySupport": false,                             "autoFillSourceField": "",                             "customConfig": "",                             "defaultValueType": "Normal",                             "description": "",                             "id": "g",                             "name": "货币",                             "numberFormat": "￥#,##0.00;￥-#,##0.00",                             "syncField": false,                             "type": "Currency"                         }                     ]                 },                 {                     "sheetId": 3,                     "recordsCount": 6,                     "fields": [                         {                             "arraySupport": false,                             "autoFillSourceField": "",                             "customConfig": "",                             "defaultValueType": "Normal",                             "description": "",                             "id": "Y",                             "name": "数字1",                             "numberFormat": "0_ ",                             "syncField": false,                             "type": "Number"                         },                         {                             "arraySupport": false,                             "autoFillSourceField": "",                             "customConfig": "",                             "defaultValueType": "Normal",                             "description": "",                             "id": "a",                             "max": 5,                             "name": "数字",                             "numberFormat": "G/通用格式",                             "syncField": false,                             "type": "Rating"                         },                         {                             "arraySupport": false,                             "autoFillSourceField": "",                             "customConfig": "",                             "defaultValueType": "Normal",                             "description": "",                             "id": "e",                             "name": "进度",                             "numberFormat": "0%",                             "syncField": false,                             "type": "Complete"                         }                     ]                 }             ]         }     ],     "maxSyncSheetLimit": 100,     "viewName": "全部数据",     "sheetName": "合并表",     "fieldSourceName": "数据源",     "fldSourceFileName": "数据源名称",     "callBackId": 1 })',
            'ScriptFile': '',
            'Arguments': '',
        },
    },
)
driver.execute_action(
    action_name='EvaluateJS',
    desc='获取cookies的wps_sid信息',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '获取cookies的wps_sid信息，用于接口调用',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': "document.cookie.split(';').reduce((obj, cookie) => cookie.trim().split('=')[0] === 'wps_sid' ? cookie.trim().split('=')[1] : obj, '');",
            'ScriptFile': '',
            'Arguments': '',
        },
    },
)
driver.execute_action(
    action_name='EvaluateJS',
    desc='获取接口分支信息',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '获取接口分支信息',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': "document.cookie.split(';').reduce((obj, cookie) => cookie.trim().split('=')[0] === 'weboffice_branch' ? cookie.trim().split('=')[1] : obj, '');",
            'ScriptFile': '',
            'Arguments': '',
        },
    },
)
driver.execute_action(
    action_name='EvaluateJS',
    desc='获取csrf信息',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '获取csrf信息',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'document.cookie.split(";").reduce((obj, cookie) => cookie.trim().split("=")[0] === "csrf" ? cookie.trim().split("=")[1] : obj, "");',
            'ScriptFile': '',
            'Arguments': '',
        },
    },
)
driver.stage(
    '3、勾选数据表“不合并”，“不合并1”，点击合并；',
    '3、勾选数据表“不合并”，“不合并1”，点击合并；',
)
driver.execute_action(
    action_name='SendHttpRequest',
    desc='发送HTTP请求',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'Method': 'POST',
            'Url': 'https://www.kdocs.cn/api/v3/office/file/%{5}.{id}%/async_job',
            'Headers': '{"content-type": "application/json","origin":"https://www.kdocs.cn","cookie": "wps_sid=%{10}.{JsValue}%; weboffice_branch=%{11}.{JsValue}%;csrf=%{12}.{JsValue}%;"}',
            'Body': '{     "job_type": "db_summary",     "file_id": "%{5}.{id}%",     "job_config": {         "db_summary_args": {             "src_app": {                 "dbs": [                     {                         "file_id": "100188412578",                         "sheet_id": 1                     },                     {                         "file_id": "100188412578",                         "sheet_id": 3                     }                 ]             },             "target_app": {                 "file_id": "%{5}.{id}%",                 "sheet_id": 2,                 "record_id_field_template": "https://www.kdocs.cn/office/:fileType/:fileId?R={base64:/C/:sheetStId/:recordId}",                 "record_id_field_name": "数据源",                 "file_info_field_name": "数据源名称"             }         }     },     "job_action": {         "hook_args": {             "hook_rule": {                 "type": "updateSheetsAllChange"             }         }     } }',
        },
    },
)
driver.execute_action(
    action_name='CustomAction',
    desc='等待内核&绘制完成',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'TimeOut': 30,
            'customActionType': 'CUSTOM',
            'customActionIdxId': 'wait_kernel_loadend',
            'InstanceIndex': '0',
            'BrowserIndex': 1,
            'BrowserType': '{{DefaultBrowserType}}',
        },
    },
)
driver.stage(
    '4、查看合并表内的“数字”，“数字1”字段；', '4、查看合并表内的“数字”，“数字1”字段；'
)
driver.execute_action(
    action_name='EvaluateJS',
    desc='切换至新建合并表',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'window.WPSOpenApi.Application.ActiveDBSheet.Sheet.SetActiveSheet(2)',
            'ScriptFile': '',
            'Arguments': '',
        },
    },
)
driver.execute_action(
    action_name='WaitTimeout',
    desc='延时等待',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'Duration': 3}},
)
driver.execute_checkpoint(
    checkpoint_name='CheckEvaluateJS',
    desc='检查合并表内显示2个数字字段图标字段：“数字”',
    app_type='WO_PC_V2',
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'window.APP.getActiveBook().getActiveSheet().getActiveDbSheetView().getField(0).getName()',
            'ScriptFile': '',
            'Arguments': '',
            'Expected': '数字',
        },
    },
)
driver.execute_checkpoint(
    checkpoint_name='CheckEvaluateJS',
    desc='检查合并表内显示2个数字字段图标字段：“数字1”',
    app_type='WO_PC_V2',
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'window.APP.getActiveBook().getActiveSheet().getActiveDbSheetView().getField(2).getName()',
            'ScriptFile': '',
            'Arguments': '',
            'Expected': '数字1',
        },
    },
)
driver.execute_checkpoint(
    checkpoint_name='CheckEvaluateJS',
    desc='检查“数字”字段第1行显示“1”',
    app_type='WO_PC_V2',
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'window.APP.getActiveBook().getActiveSheet().getActiveDbSheetView().getCellInfo(0,0).getCellString()',
            'ScriptFile': '',
            'Arguments': '',
            'Expected': '1 ',
        },
    },
)
driver.execute_checkpoint(
    checkpoint_name='CheckEvaluateJS',
    desc='检查“数字1”字段第7行显示“2”',
    app_type='WO_PC_V2',
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.1',
            'ScriptText': 'window.APP.getActiveBook().getActiveSheet().getActiveDbSheetView().getCellInfo(6,2).getCellString()',
            'ScriptFile': '',
            'Arguments': '',
            'Expected': '2 ',
        },
    },
)
driver.execute_action(
    action_name='WaitTimeout',
    desc='延时等待',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'Duration': 3}},
)

driver.execute_action(
    action_name='QuitBrowser',
    desc='关闭浏览器',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'BrowserSelector': '*'}},
)
driver.quit()
