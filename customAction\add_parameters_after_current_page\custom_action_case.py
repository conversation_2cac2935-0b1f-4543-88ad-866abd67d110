from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    在当前页面链接后加参数 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('parameters', 'Parameters')

    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        parameters = self.get_arg('parameters')
        # 检查当前URL是否已包含问号，如果包含则使用&连接，否则使用?连接
        script = f"""
        (function() {{
            var currentUrl = window.location.href;
            var separator = currentUrl.includes('?') ? '&' : '?';
            window.location.href = currentUrl + separator + '{parameters}';
        }})();
        """
        self.execute_script(browser_selector, script)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






