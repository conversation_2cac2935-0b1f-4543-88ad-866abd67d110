from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB创建视图 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('view_type','ViewType')

    def execute(self):
        view_type = self.get_arg('view_type')
        browser_selector = self.get_arg('browser_selector')
        self.create_view(browser_selector,view_type)

    def create_view(self,browser_selector,view_type):
        if view_type == "日历视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createCalendarView('日历视图');"
        elif view_type == "表单视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createFormView('表单视图');"
        elif view_type == "画册视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createGalleryView('画册视图');"
        elif view_type == "甘特视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createGanttView('甘特视图');"
        elif view_type == "表格视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createGridView('表格视图');"
        elif view_type == "看板视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createKanbanView('看板视图');"
        elif view_type == "查询视图":
            js = f"window.APP.getActiveSheet().getDbSheetViews().createQueryView('查询视图');"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






