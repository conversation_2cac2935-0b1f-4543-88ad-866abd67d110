from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger
import hashlib
import time
import json
from datetime import datetime
from wsgiref.handlers import format_date_time

class CustomAction(BaseAction):
    """
    销毁云文档
    """
    def parse_args(self, args):
        self.add_required_arg('gid', 'GroupId') # 分组ID
        self.add_required_arg('pid', 'ParentId') # 父文件夹ID
        self.add_required_arg('file_id', 'FileId') # 文件ID
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie
        self.add_required_arg('secret_key', 'SecretKey')  # 密钥
        self.add_required_arg('access_key', 'AccessKey')  # 访问密钥
        self.add_arg('branch', 'Branch', '')  # 分支名称
        self.add_arg('test_platform', 'TestPlatform', 'kdocs')  # 测试平台

    def execute(self) -> int:
        gid = self.get_arg('gid')
        cookies = self.get_arg('cookies')
        file_id = self.get_arg('file_id')
        # 获取销毁文档会话所需的参数
        secret_key = self.get_arg('secret_key')
        access_key = self.get_arg('access_key')
        branch = self.get_arg('branch')
        pid = self.get_arg('pid')
        test_platform = self.get_arg('test_platform')
        
        # 转换cookies
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        # 从cookies_dict中获取wps_sid
        wps_sid = cookies_dict['wps_sid']
        if not wps_sid:
            logger.error('wps_sid不存在')
            return 1
        # 从cookies_dict中获取csrf
        csrf_token = cookies_dict['csrf']
        if not csrf_token:
            logger.error('csrf不存在')
            return 1
        
        # 先尝试销毁文档会话
        if secret_key and access_key:
            try:
                logger.info(f'销毁云文档会话: {file_id}')
                self.destroy_file_session(
                    id=file_id, 
                    secret_key=secret_key, 
                    access_key=access_key,
                    BRANCH=branch,
                    WPS_SID=wps_sid,
                    GROUP_ID=gid,
                    PARENT_ID=pid,
                    TEST_PLATFORM=test_platform
                )
                logger.info(f'销毁云文档会话成功: {file_id}')
            except Exception as e:
                logger.error(f'销毁云文档会话失败: {e}')
        
        # 然后删除文档
        logger.info(f'删除云文档: {gid} {file_id}')
        url = f'https://www.kdocs.cn/3rd/drive/api/v3/groups/{gid}/files/{file_id}'
        res = requests.delete(url, json={"csrfmiddlewaretoken": csrf_token}, cookies=cookies_dict, verify=False)
        if res.status_code == 200:
            logger.info(f'删除云文档成功: {file_id}')
            return 0
        else:
            logger.error(f'删除云文档失败: {res.text}')
            return 1

    def destroy_file_session(self, id, *, secret_key, access_key, BRANCH, WPS_SID, GROUP_ID, PARENT_ID, TEST_PLATFORM='kdocs'):
        """
        销毁云文档会话
        """
        def get_headers(uri, secret_key, access_key):
            content_md5 = hashlib.md5(uri.encode('utf-8')).hexdigest()
            content_type = "application/json"
            now = datetime.now()
            stamp = time.mktime(now.timetuple())
            date = format_date_time(stamp)

            authorization = "weboffice:" + hashlib.sha1((secret_key+content_md5+content_type+date).encode('utf-8')).hexdigest()

            headers = {
                "Access-Id": access_key,
                "Authorization": authorization,
                "Content-Md5": content_md5,
                "Content-Type": content_type,
                "Date": date,
                "Cookie": f"weboffice_branch={BRANCH};wps_sid={WPS_SID}"
            }
            return headers

        uri = f'/api/v3/developer/file/{id}/destroy/session'
        headers = get_headers(uri, secret_key, access_key)
        ORIGIN = 'https://www.kdocs.cn'

        if TEST_PLATFORM == 'wwo':
            WWO_APP_ID = '796ae09c69f84617b796ec2fbaf4555c'
            ORIGIN = 'https://wwo.wps.cn'
            id = str(id).split('-')[-1]
            url = f'https://test.wwo.wps.cn/getUrl?&permission=write&sid=null&groupid={GROUP_ID}&parentid={PARENT_ID}&ftype=file&fileid={id}'
            res = requests.get(
                url,
                cookies={"wps_sid": WPS_SID, "weboffice_branch": BRANCH, f"{WWO_APP_ID}_token": f"openplatform{WPS_SID}"},
                verify=False
            )
            headers['x-user-token'] = f"openplatform{WPS_SID}"
            headers['x-user-query'] = json.loads(res.text)['wpsUrl'].split('?')[1]

        res = requests.get(ORIGIN+uri, headers=headers, verify=False)
        logger.info(f"关闭文档服务: {res.text}")
        return res.json() if res.status_code == 200 else None

if __name__ == '__main__':
    pass
