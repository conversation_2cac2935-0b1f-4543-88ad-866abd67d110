from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger

class CustomAction(BaseAction):
    """
    合并ET的Sheet文档
    """
    def parse_args(self, args):
        self.add_required_arg('file_id_list', 'FileIdList') # 源文件ID列表
        self.add_required_arg('target_file_id', 'TargetFileId') # 目标文件ID
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie

    def execute(self) -> int:
        file_id_list = self.get_arg('file_id_list')
        target_file_id = self.get_arg('target_file_id')
        cookies = self.get_arg('cookies')
        
        # 逗号分割，然后校验file_id_list长度，最少1个
        file_id_list = file_id_list.split(',')
        if len(file_id_list) < 1:
            raise Exception("源文件ID列表不能为空")
        
        try:
            #将列表格式的cookies转变为headers里可用的格式
            cookie_str = ';'.join([f'{cookie["name"]}={cookie["value"]}' for cookie in cookies])
            headers = {
                "Origin": "https://www.kdocs.cn",
                "Cookie": f"{cookie_str}"
            }
            files = []
            for file_id in file_id_list:
                relation = {"is_url": False, "relation": f"{file_id}"}
                files.append(relation)

            body1 = {
                "files": files
            }
            url1 = f"https://www.kdocs.cn/api/v3/office/file/{target_file_id}/relation"
            response1 = requests.request(url=url1, method="POST", headers=headers, json=body1, verify=False)

            sub_info1 = []

            for file_id in file_id_list:
                nameurl1 = f"https://www.kdocs.cn/api/v3/office/file/{file_id}/filemerge/sheetname"
                nameurl1_response = requests.request(url=nameurl1, method="GET", headers=headers, verify=False)
                sheets = nameurl1_response.json()['sheets']

                data = []
                for j in sheets:
                    dict = {"sheet_name": j}
                    data.append(dict)
                sub_info = {
                    "file_id": file_id,
                    "data": data,
                    "is_current_merge": True
                }
                sub_info1.append(sub_info)

            nameurl2 = f"https://www.kdocs.cn/api/v3/office/file/{target_file_id}/filemerge/sheetname"
            nameurl2_response = requests.request(url=nameurl2, method="GET", headers=headers, verify=False)
            body2 = {
                "file_title": nameurl2_response.json()['file_name'].split('.')[0],
                "sub_info": sub_info1,
                "target_file_id": target_file_id
            }
            url2 = f"https://www.kdocs.cn/api/v3/office/filemerge/subscribe"
            response2 = requests.request(url=url2, method="POST", headers=headers, json=body2, verify=False)

            result = []
            for file_id in file_id_list:
                nameurl3 = f"https://www.kdocs.cn/api/v3/office/file/{file_id}/filemerge/sheetname"
                nameurl3_response = requests.request(url=nameurl3, method="GET", headers=headers, verify=False)
                sheets = nameurl3_response.json()['sheets']
                data = []
                for j in sheets:
                    dict = {"sheet_name": j}
                    data.append(dict)
                file_merge_info = {
                    "data": data,
                    "docName": nameurl3_response.json()['file_name'].split('.')[0],
                    "file_id": nameurl3_response.json()['file_id']
                }
                result.append(file_merge_info)
            body3 = {
                "is_first_merge": True,
                "target_file_id": target_file_id,
                "file_merge_info": result,
                "attr": {
                    "bookErrTips": "错误，无法从以下工作簿中读取数据：%1",
                    "isMarkDataSrc": False,
                    "isRemoveDuplicates": False,
                    "isSubscribe": False,
                    "namePrefix": "汇总数据",
                    "sheetErrTips": "错误，无法从以下工作表中读取数据：%1（位于：%2）",
                    "titleRowCnt": 0,
                    "isNewFile": False
                }
                #
            }
            url3 = f"https://www.kdocs.cn/api/v3/office/filemerge"
            response3 = requests.request(url=url3, method="POST", headers=headers, json=body3, verify=False)
            self.resultdata=response3.json()['result']
            logger.info(f'响应码：{response3.status_code}，接口返回：{response3.json()}')
            if response3.status_code == 200:
                return True
            else:
                raise Exception(f"合并失败，错误信息：{response3.json()}")

      
        except Exception as e:
            raise Exception(f"合并失败，错误信息：{e}")



if __name__ == '__main__':
    pass
