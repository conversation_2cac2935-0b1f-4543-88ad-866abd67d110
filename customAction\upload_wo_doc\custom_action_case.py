from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger
import uuid
import os
import json


class CustomAction(BaseAction):
    """
    上传云文档
    """
    def parse_args(self, args):
        self.add_required_arg('gid', 'GroupId') # 分组ID
        self.add_required_arg('pid', 'ParentId') # 父文件夹ID
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie
        self.add_required_arg('file_path', 'FilePath') # 文件路径

    def execute(self) -> int:
        gid = self.get_arg('gid')
        pid = self.get_arg('pid')
        cookies = self.get_arg('cookies')
        file_path = self.get_arg('file_path')
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        
        # 拼接工作目录
        work_dir = self.driver_context.case_conf.run_case_dir
        file_path = os.path.join(work_dir, file_path)
        # 判断文件是否存在
        if not os.path.exists(file_path):
            logger.error(f'文件不存在: {file_path}')
            raise Exception(f'文件不存在: {file_path}')
        
        file_name = str(uuid.uuid1()) + '-' + os.path.basename(file_path)
        data = {
            'groupid': int(gid),
            'parentid': int(pid),
            'size': os.stat(file_path).st_size,
            'name': file_name,
            'store': 'ks3',
            'method': 'POST',
            'encrypt': 'true'
        }

        url = 'https://www.kdocs.cn/3rd/drive/api/files/upload/request'

        # 使用 requests.get() 发送请求，params 参数会自动处理查询参数
        response = requests.get(url, params=data, cookies=cookies_dict, verify=False)

        if response.status_code == 200:
            result = response.json()  # 自动解析 JSON 响应
            
        logger.info(f'上传云文档请求结果: {file_name}, {result}')
        
        file = {'file': open(file_path, 'rb')}
        data = {
            'key': result['key'], 'Policy': result['Policy'],
            'Signature': result['Signature'],
            'KSSAccessKeyId' : result['KSSAccessKeyId'],
            'x-kss-newfilename-in-body': result['x-kss-newfilename-in-body'],
            'x-kss-server-side-encryption': result['x-kss-server-side-encryption'],
            'submit': 'Upload to KS3'}
        upload_result = requests.post(url=result['url'], data=data, files=file, cookies=cookies_dict, verify=False)
        
        logger.info(f'上传云文档结果: {upload_result.text}')
        
        json_data = {
            'groupid': int(gid),
            'isUpNewVer': False,
            'name': file_name,
            'parent_path': [],
            'parentid': int(pid),
            'sha1': json.loads(upload_result.text)['newfilename'],
            'size': os.stat(file_path).st_size,
            'store': 'ks3',
            'etag': upload_result.headers['ETag'].replace('"', ''),
        }

        headers = {'Origin': 'https://www.kdocs.cn'}
        file_url = f'https://www.kdocs.cn/3rd/drive/api/v5/files/file'
        res = requests.post(url=file_url, json=json_data, headers=headers, cookies=cookies_dict, verify=False)
        if res.status_code != 200:
            logger.error(f'上传云文档失败: {res.text}')
            raise Exception(f'上传云文档失败: {res.text}')
        res_json = res.json()
        logger.info(f'上传云文档成功: {res_json}')
        
        
        # 输出变量
        self.set_step_variable('id', res_json['id'])
        self.set_step_variable('link_id', res_json['link_id'])
        self.set_step_variable('link_url', res_json['link_url'])
        
        return res_json['id']


if __name__ == '__main__':
    pass
