from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    ET检查单元格字体 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('range_location', 'RangeLocation')

    def execute(self) -> str:
        browser_selector = self.get_arg('browser_selector')
        range_location = self.get_arg('range_location')
        return self.get_range_value(browser_selector,range_location)

    def get_range_value(self,browser_selector,range_location) -> str:
        js = f"async()=>await WPSOpenApi.Application.Range('{range_location}').Font.Name"
        return self.execute_script(browser_selector,js)['actual']

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






