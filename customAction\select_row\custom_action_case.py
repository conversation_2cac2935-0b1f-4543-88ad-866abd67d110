from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    选中行 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('component','Component')
        self.add_required_arg('row_number','RowNumber')

    def execute(self):
        component = self.get_arg('component')
        browser_selector = self.get_arg('browser_selector')
        row_number = self.get_arg('row_number')
        self.select_row(browser_selector,component,row_number)

    def select_row(self,browser_selector,component,row_number):
        if component == 'ET' or component == 'AS':
            js = f"async()=>await window.WPSOpenApi.Application.Range('A{row_number}').EntireRow.Select()"
        elif component == 'DB':
            js = f"async()=>await window.WPSOpenApi.Application.ActiveDBSheet.Selection.SelectRow({row_number})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






