from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB设置单元格数据 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('localcation','Localcation') 
        self.add_required_arg('range_value','RangeValue')

        
    def execute(self):
        range_value = self.get_arg('range_value')
        localcation = self.get_arg('localcation')
        browser_selector = self.get_arg('browser_selector')
        self.set_range_value(browser_selector,localcation,range_value)

    def set_range_value(self,browser_selector,localcation,range_value):
        js = f"""
                var view = APP.getActiveSheet().getActiveDbSheetView();
                rg = view.createRange();
                rg.selectCell({localcation});
                rg.setActiveCell(1);
                rg.setCellValue('{range_value}')
                """
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






