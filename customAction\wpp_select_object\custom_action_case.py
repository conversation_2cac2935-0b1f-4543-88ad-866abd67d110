from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    WPP选中对象 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('object_id', 'ObjectID')
        self.add_required_arg('browser_selector','BrowserSelector')

    def execute(self):

        object_id = self.get_arg('object_id')
        browser_selector = self.get_arg('browser_selector')
        self.get_object_id(browser_selector,object_id)

    def get_object_id(self,browser_selector,object_id):
        js = f"window.APP.shell.doc.getLocalSelection().selectShapeAt({object_id})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






