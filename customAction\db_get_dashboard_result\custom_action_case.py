from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
from weboffice_v2.pc.component.actions.base import BaseAction
import requests
import time
import json
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    DB获取仪表盘结果维度名 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('chart_index', 'ChartIndex')

    def get_sheetId(self, browser_selector) -> int:
        js = "window.APP.getActiveSheet().getStId()"
        sheet_id = self.execute_script(browser_selector, js)['actual']
        return sheet_id
    
    def get_extId(self,browser_selector,chart_index) -> str:
        js = f"window.APP.getActiveSheet().getWebExtensions().data.private[{chart_index}].private.webextension.private.normalProperty.private.key"
        extid = self.execute_script(browser_selector,js)['actual']
        return extid
    
    def execute(self) :
        browser_selector = self.get_arg('browser_selector')
        chart_index = self.get_arg('chart_index')

        # js = f"document.querySelectorAll('.layout-item-extension-wrapper').length"
        # chart_length = int(self.execute_script(browser_selector,js)['actual'])
        # if chart_index > chart_length:
        #     raise Exception(f"欲比较的图表数量超过实际图表数量")
        
        sheet_id = int(self.get_sheetId(browser_selector))
        ext_id = self.get_extId(browser_selector, chart_index)


        jsData = f"window.APP.getWorksheets().getItemByStId({sheet_id}).getWebExtensions().data"
        dataValue = self.execute_script(browser_selector,jsData)
        if dataValue:
            # 设置全局变量存储结果
            setup_js = """
            window.__dashboardResultCategories = null;
            """
            self.execute_script(browser_selector, setup_js)
            
            # 将结果存储在全局变量中
            result_categories_js = f"""
            window.APP.getWorksheets().getItemByStId({sheet_id}).getWebExtensions().itemByKey('{ext_id}').queryModuleContent(res => {{

                const a = res.result.categories;
                const processedData = JSON.parse(JSON.stringify(a));
                processedData.forEach(item => {{
                    try {{
                        item.identifier = JSON.parse(item.identifier);
                    }} catch (e) {{
                        console.error('解析错误:', e);
                        item.identifier = item.identifier;
                    }}
                }});
                window.__dashboardResultCategories = processedData;
                console.log('结果已保存到 window.__dashboardResultCategories');
            }});
            """
            self.execute_script(browser_selector, result_categories_js)
            # 确保回调执行完成
            time.sleep(1)
            
            # 获取存储的结果
            get_result_js = "window.__dashboardResultCategories;"
            result = self.execute_script(browser_selector, get_result_js)['actual']
            
            result_type_js = f"""
                const resultType = window.APP.getWorksheets().getItemByStId({sheet_id}).getWebExtensions().itemByKey('{ext_id}').getPropertiesValue()
                const jsonType = JSON.parse(JSON.stringify(resultType));
                const chartInfoStr = jsonType['chart-info'];
                const chartInfoObj = JSON.parse(chartInfoStr);
                chartInfoObj['type']
            """
            result_type = self.execute_script(browser_selector, result_type_js)['actual']

            # 处理Unicode转义序列
            if result:
                result = self.decode_unicode_in_dict(result)
                result = json.loads(result, strict=False)
                
                processed_result = []
                
                # 如果是饼图，计算总数用于百分比计算
                total_data = 0
                if result_type == "BasicPie":
                    for item in result:
                        if len(item['data']) > 0:
                            # 假设饼图每个项只有一个数据值
                            try:
                                total_data += float(item['data'][0])
                            except (ValueError, TypeError):
                                # 无法转换为数字，跳过
                                pass
                
                for item in result:
                    # 删除name中的换行符
                    item_name = item['name'].replace('\n', '')
                    
                    if len(item['data']) > 1:
                        # data长度大于1，返回所有数据
                        data_values = [str(d).replace('\n', '') for d in item['data']]
                        
                        # 如果是BarLine类型且data至少有两个值，特殊处理第二个值
                        if result_type == "BarLine" and len(item['data']) >= 2:
                            try:
                                # 获取第二个值乘以100后取整数作为百分比
                                second_value = float(item['data'][1])
                                percentage = int(second_value * 100)
                                processed_result.append(f"{item_name}:{data_values[0]}:{percentage}%")
                            except (ValueError, TypeError, IndexError):
                                # 无法处理，使用原始格式
                                processed_result.append(f"{item_name}:{','.join(data_values)}")
                        else:
                            processed_result.append(f"{item_name}:{','.join(data_values)}")
                    else:
                        # 只返回第一个数据
                        data_value = str(item['data'][0]).replace('\n', '') if item['data'] else ""
                        
                        # 如果是饼图且可以计算百分比
                        if result_type == "BasicPie" and total_data > 0 and item['data']:
                            try:
                                value = float(item['data'][0])
                                percentage = (value / total_data) * 100
                                # 精确四舍五入到2位小数
                                percentage = Decimal(str(percentage)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                                processed_result.append(f"{item_name}:{data_value}:{percentage}%")
                            except (ValueError, TypeError):
                                # 无法计算百分比，使用原始格式
                                processed_result.append(f"{item_name}:{data_value}")
                        else:
                            processed_result.append(f"{item_name}:{data_value}")
                
                json_output = json.dumps(processed_result, ensure_ascii=False)
                return json_output
        else:
            raise Exception(f"dataValue is None")
        
        
    

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


    def decode_unicode_in_dict(self, obj):
        """递归处理字典中的Unicode转义序列"""
        if isinstance(obj, dict):
            return {k: self.decode_unicode_in_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.decode_unicode_in_dict(item) for item in obj]
        elif isinstance(obj, str):
            try:
                # 检查字符串是否包含Unicode转义序列
                if any(r'\u' in obj[i:i+2] for i in range(len(obj)-1)):
                    return obj.encode('utf-8').decode('unicode_escape')
                else:
                    return obj
            except:
                return obj
        else:
            return obj
    
    

if __name__ == '__main__':
    pass






