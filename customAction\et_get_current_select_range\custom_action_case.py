from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    ET检查当前选中区域 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('row_begin','RowBegin')
        self.add_required_arg('column_begin','ColumnBegin')
        self.add_required_arg('row_end','RowEnd')
        self.add_required_arg('column_end','ColumnEnd')

    def execute(self) -> bool:
        browser_selector = self.get_arg('browser_selector')
        row_begin = self.get_arg('row_begin')
        column_begin = self.get_arg('column_begin')
        row_end = self.get_arg('row_end')
        column_end = self.get_arg('column_end')
        return self.get_range_value(browser_selector,row_begin,column_begin,row_end,column_end)

    def get_range_value(self,browser_selector,row_begin,column_begin,row_end,column_end) -> bool:
        js = f"async()=>await window.WPSOpenApi.Application.Selection.Row==={row_begin}&&await window.WPSOpenApi.Application.Selection.Column==={column_begin}&&await window.WPSOpenApi.Application.Selection.RowEnd==={row_end}&&await window.WPSOpenApi.Application.Selection.ColumnEnd==={column_end}"
        return self.execute_script(browser_selector,js)['actual']

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






