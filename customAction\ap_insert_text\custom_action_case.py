from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    AP输入内容 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('text_content','TextContent') 

        
    def execute(self):
        text_content = self.get_arg('text_content')
        browser_selector = self.get_arg('browser_selector')
        self.insert_text(browser_selector,text_content)

    def insert_text(self,browser_selector,text_content):
        js = f"window.APP.OTL.commands['text']({{ cmd: 'TEXT_CMD.INSERT', params: {{ value: '{text_content}' }} }})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






