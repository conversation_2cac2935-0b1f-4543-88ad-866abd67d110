from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests


class CustomAction(BaseCheckpoint):
    """
    检查当前网址是否包含 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('page_link','PageLink')

    def execute(self)->bool:
        browser_selector = self.get_arg('browser_selector')
        page_link = self.get_arg('page_link')
        result = self.execute_script(browser_selector,f"(window.location.href).includes('{page_link}')")['actual']
        return result


    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






