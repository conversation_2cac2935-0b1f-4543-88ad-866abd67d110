from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    AP滚动至文档尾部 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')

        
    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        self.scroll_to_bottom(browser_selector)

    def scroll_to_bottom(self,browser_selector):
        js = f"""
        let bottom = window.APP.OTL.view.state.doc.content.size;
        (async()=>{{
            await window.WPSOpenApi.ready(); 
            await WPSOpenApi.Application.ActiveOutline.Editor.View.ScrollToPos(bottom,true);
        }})();
        """
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






