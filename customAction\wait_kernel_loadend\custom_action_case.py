from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP
import time


class CustomAction(BaseCheckpoint):
    """
    等待内核数据计算完成 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('time_out', 'TimeOut')

    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        time_out = self.get_arg('time_out')
        self.wait_kernel_loadend(browser_selector,time_out)

    def wait_kernel_loadend(self,browser_selector,time_out):
        
        time_count = 0
        type_js = f"fileInfo.office_type"
        office_type = self.execute_script(browser_selector,type_js)['actual']
        
        if office_type == 'w':
            kernel_js = f"async()=>await APP.waitLocalRenderReady(60000);"
            kernel_status = self.execute_script(browser_selector,kernel_js)['actual']
            while kernel_status == 'false':
                time.sleep(2)
                kernel_status = self.execute_script(browser_selector,kernel_js)['actual']
                time_count += 2
                if time_count > time_out:
                    raise Exception(f'等待{time_out}s未计算完成，已退出等待')
            test_js = f"window.APP.testIsIdle();"
            test_status = self.execute_script(browser_selector,test_js)['actual']
            while test_status == 'false':
                time.sleep(2)
                test_status = self.execute_script(browser_selector,test_js)['actual']
                time_count += 2
                if time_count > time_out:
                    raise Exception(f'等待{time_out}s未计算完成，已退出等待')
            pass
            
        elif office_type == 'k' or office_type == 's' or office_type == 'd':
            kernel_js = f"""
            async () => {{
                const waitForWholeIdle = (timeout = 5000) => {{
                    return new Promise((resolve, reject) => {{
                        const startTime = Date.now();
                        const intervalId = setInterval(() => {{
                            const isWholeIdle = window.APP.session.isWholeIdle();
                            const method = window.APP.session.sender.sendingData?.method;
                            const isCalcStatus = window.APP.getCalcStatus();
                            if (isCalcStatus == 'finish' && isCalcStatus != 'suspend' && isWholeIdle && !method) {{
                                clearInterval(intervalId);
                                resolve(true);
                            }} else if (Date.now() - startTime >= timeout) {{
                                clearInterval(intervalId);
                                resolve(true);
                            }}
                        }}, 100);
                    }});
                }};
                return await waitForWholeIdle(60000);
            }}
            """
            kernel_status = self.execute_script(browser_selector,kernel_js)['actual']
            if kernel_status == 'true':
                test_js = f"window.APP.testIsIdle();"
                test_status = self.execute_script(browser_selector,test_js)['actual']
                while test_status == 'false':
                    time.sleep(2)
                    test_status = self.execute_script(browser_selector,test_js)['actual']
                    time_count += 2
                    if time_count > time_out:
                        raise Exception(f'等待{time_out}s未计算完成，已退出等待')
                pass
            
        elif office_type == 'p':
            kernel_js = f"window.APP.session.isWholeIdle();"
            kernel_status = self.execute_script(browser_selector,kernel_js)['actual']
            while kernel_status == 'false':
                time.sleep(2)
                kernel_status = self.execute_script(browser_selector,kernel_js)['actual']
                time_count += 2
                if time_count > time_out:
                    raise Exception(f'等待{time_out}s未计算完成，已退出等待')
            test_js = f"window.APP.testIsIdle();"
            test_status = self.execute_script(browser_selector,test_js)['actual']
            while test_status == 'false':
                time.sleep(2)
                test_status = self.execute_script(browser_selector,test_js)['actual']
                time_count += 2
                if time_count > time_out:
                    raise Exception(f'等待{time_out}s未计算完成，已退出等待')
            pass
    

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






