from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    ET选中单元格&区域 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('range_value','RangeValue')

    def execute(self):
        range_value = self.get_arg('range_value')
        browser_selector = self.get_arg('browser_selector')
        self.et_select_range(browser_selector,range_value)

    def et_select_range(self,browser_selector,range_value):
        js = f"window.WPSOpenApi.Application.ActiveSheet.Range('{range_value}').Select()"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






