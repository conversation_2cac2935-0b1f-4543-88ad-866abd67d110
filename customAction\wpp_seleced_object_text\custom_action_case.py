from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    WPP选择已选中对象内的文本（文本从0开始） 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('start', 'Start')
        self.add_required_arg('end', 'End')


    def execute(self):

        start = self.get_arg('start')
        end = self.get_arg('end')
        browser_selector = self.get_arg('browser_selector')
        self.get_wpp_seleced_object_text(browser_selector,start, end)

    def get_wpp_seleced_object_text(self,browser_selector,start,end):
        js = f"window.APP.shell.doc.getLocalSelection().selectText({start},{end})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






