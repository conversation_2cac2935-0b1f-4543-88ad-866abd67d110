from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    XXX自定义及动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_arg('param1', 'Param1', '')
        self.add_arg('param2', 'Param2', '')

    def execute(self) -> int:
        print('XXXX 自定义动作执行')

        param1 = self.get_arg('param1')
        param2 = self.get_arg('param2')

        # 可以调用其他库
        response = requests.get(param2)
        print(response.text)

        # 可以获取指定步骤的变量值：第param1步骤的body变量
        body = self.driver_context.get_step_variable(f"%{{{param1}}}.{{body}}%")
        print(body)

        # 可以组合调用其他动作
        self.driver_context.driver.execute_action(action_name='GoTo',
                                                  args={'BrowserSelector': None, 'Url': 'https://www.baidu.com/'})

        # 可以输出当前步骤的变量
        self.set_step_variable('body', 'body')

        return 0


if __name__ == '__main__':
    pass






