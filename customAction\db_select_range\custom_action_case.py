from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB选中单元格 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('range_row','RangeRow')
        self.add_required_arg('range_column','RangeColumn')

    def execute(self):

        range_row = self.get_arg('range_row')
        range_column = self.get_arg('range_column')
        browser_selector = self.get_arg('browser_selector')
        self.select_range(browser_selector,range_row,range_column)

    def select_range(self,browser_selector,range_row,range_column):
        js = f"""async()=>{{
            await window.WPSOpenApi.Application.ActiveDBSheet.Selection.SelectCell({range_row}, {range_column});
            window.APP.focusDocView();
        }}"""
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






