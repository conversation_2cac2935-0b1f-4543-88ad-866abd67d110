from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger

class CustomAction(BaseAction):
    """
    删除云文档
    """
    def parse_args(self, args):
        self.add_required_arg('gid', 'GroupId') # 分组ID
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie
        self.add_required_arg('file_id', 'FileId') # 文件ID

    def execute(self) -> int:
        gid = self.get_arg('gid')
        cookies = self.get_arg('cookies')   
        file_id = self.get_arg('file_id')
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        # 从cookies_dict中获取csrf_token
        csrf_token = cookies_dict['csrf']
        if not csrf_token:
            logger.error('csrf_token不存在')
            return 1
        
        logger.info(f'删除云文档: {gid} {file_id}')
        url = f'https://www.kdocs.cn/3rd/drive/api/v3/groups/{gid}/files/{file_id}'
        res = requests.delete(url, json={"csrfmiddlewaretoken": csrf_token}, cookies=cookies_dict, verify=False)
        if res.status_code == 200:
            logger.info(f'删除云文档成功: {file_id}')
            return 0
        else:
            logger.error(f'删除云文档失败: {res.text}')
            return 1

if __name__ == '__main__':
    pass
