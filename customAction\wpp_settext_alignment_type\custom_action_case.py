from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    WPP设置选中的文本对齐方式(横向) 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('alignment_type', 'AlignmentType')


    def execute(self):
        alignment_type = self.get_arg('alignment_type')
        browser_selector = self.get_arg('browser_selector')
        self.wpp_set_text_alignment_type(browser_selector,alignment_type)

    def wpp_set_text_alignment_type(self,browser_selector,alignment_type):
        js = f"window.APP.shell.editor.paragraph.setAlignment({alignment_type})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






