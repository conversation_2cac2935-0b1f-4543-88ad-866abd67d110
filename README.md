# 介绍
本项目用于制作调试自定义动作

# 步骤
1. 在customAction目中创建一个目录，如563518116122988546（调试时目录名称随意）
2. 创建__init__.py和custom_action_case.py (或者直接将563518116122988546复制一份，修改名称即可)
3. 在case.py中引用自定义动作
```py
driver.execute_action(action_name='CustomAction', desc='', app_type='WO_PC_V2', is_must_execute=False, is_must_pass=True, args={'Timeout': 300, 'Descr': '', 'Arg': {'customActionIdxId': '563518116122988546', 'Param1': 2, 'Param2': 'https://react.dev'}})
```
4. 编写自定义动作。
自定义动作：
```py
from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    XXX自定义及动作
    """

    def parse_args(self, args):
        # 在这里处理入参

    def execute(self) -> int:
        # 在这里编写逻辑


if __name__ == '__main__':
    pass

```
自定义检查点：
```py
from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseCheckpoint):
    """
    XXX自定义及动作
    """

    def parse_args(self, args):
        # 在这里处理入参

    def execute(self) -> int:
        # 在这里编写逻辑

if __name__ == '__main__':
    pass
```
5. 断点调试
