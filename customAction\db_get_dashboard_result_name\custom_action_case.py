from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
from weboffice_v2.pc.component.actions.base import BaseAction
import requests
import time
import json
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP
import re


class CustomAction(BaseCheckpoint):
    """
    DB获取仪表盘结果 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('chart_index', 'ChartIndex')

    def get_sheetId(self, browser_selector) -> int:
        js = "window.APP.getActiveSheet().getStId()"
        sheet_id = self.execute_script(browser_selector, js)['actual']
        return sheet_id
    
    def get_extId(self,browser_selector,chart_index) -> str:
        js = f"window.APP.getActiveSheet().getWebExtensions().data.private[{chart_index}].private.webextension.private.normalProperty.private.key"
        extid = self.execute_script(browser_selector,js)['actual']
        return extid
    
    def execute(self) :
        browser_selector = self.get_arg('browser_selector')
        chart_index = self.get_arg('chart_index')

        # js = f"document.querySelectorAll('.layout-item-extension-wrapper').length"
        # chart_length = int(self.execute_script(browser_selector,js)['actual'])
        # if chart_index > chart_length:
        #     raise Exception(f"欲比较的图表数量超过实际图表数量")
        
        sheet_id = int(self.get_sheetId(browser_selector))
        ext_id = self.get_extId(browser_selector, chart_index)


        jsData = f"window.APP.getWorksheets().getItemByStId({sheet_id}).getWebExtensions().data"
        dataValue = self.execute_script(browser_selector,jsData)
        if dataValue:
            # 设置全局变量存储结果
            setup_js = """
            window.__dashboardResultCategories = null;
            """
            self.execute_script(browser_selector, setup_js)
            
            # 将结果存储在全局变量中
            query_js = f"""
            window.APP.getWorksheets().getItemByStId({sheet_id}).getWebExtensions().itemByKey('{ext_id}').queryModuleContent(res => {{

                const a = res.result.categories;
                const processedData = JSON.parse(JSON.stringify(a));
                processedData.forEach(item => {{
                    try {{
                        item.identifier = JSON.parse(item.identifier);
                    }} catch (e) {{
                        console.error('解析错误:', e);
                        item.identifier = item.identifier;
                    }}
                }});
                window.__dashboardResultCategories = processedData;
                console.log('结果已保存到 window.__dashboardResultCategories');
            }});
            """
            self.execute_script(browser_selector, query_js)
            # 确保回调执行完成
            time.sleep(1)
            
            # 获取存储的结果
            get_result_js = "window.__dashboardResultCategories;"
            result = self.execute_script(browser_selector, get_result_js)['actual']

            # 处理Unicode转义序列
            if result:
                result = self.decode_unicode_in_dict(result)
                # 在JSON解析之前清理控制字符
                if isinstance(result, str):
                    result = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', result)
                result = json.loads(result)
                result = [item['name'].replace('\n', '').replace('\r', '').replace('\t', '').strip() for item in result]
                json_output = json.dumps(result, ensure_ascii=False)
                return json_output
        else:
            raise Exception(f"dataValue is None")
        
        
    

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


    def decode_unicode_in_dict(self, obj):
        """递归处理字典中的Unicode转义序列"""
        if isinstance(obj, dict):
            return {k: self.decode_unicode_in_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.decode_unicode_in_dict(item) for item in obj]
        elif isinstance(obj, str):
            try:
                # 检查字符串是否包含Unicode转义序列
                if any(r'\u' in obj[i:i+2] for i in range(len(obj)-1)):
                    return obj.encode('utf-8').decode('unicode_escape')
                else:
                    return obj
            except:
                return obj
        else:
            return obj
    
    

if __name__ == '__main__':
    pass






