from weboffice_v2.pc.component.actions.base import BaseAction
import requests
import re


class CustomAction(BaseAction):
    """
    选中列 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('component','Component')
        self.add_required_arg('column_number','ColumnNumber')

    def execute(self):
        component = self.get_arg('component')
        browser_selector = self.get_arg('browser_selector')
        column_number = self.get_arg('column_number')
        self.select_column(browser_selector,component,column_number)
        

    def select_column(self,browser_selector,component,column_number):
        if component == 'ET' or component == 'AS':
            # 是否数字
            if not re.search(r'[A-Za-z]', column_number):
                column_number = self.number_to_excel_format(int(column_number))
            js = f"async()=>await window.WPSOpenApi.Application.Range('{column_number}').EntireColumn.Select()"
            
        elif component == 'DB':
            # 是否包含字母
            if re.search(r'[A-Za-z]', column_number):
                column_letters = self.extract_column_letters(column_number)
                column_number = self.excel_column_to_number(column_letters)
            # 只有数字使用原值
            js = f"async()=>await window.WPSOpenApi.Application.ActiveDBSheet.Selection.SelectCol({column_number})"
            
        self.execute_script(browser_selector,js)
        
    def extract_column_letters(self, column_number):
        match = re.match(r'^([A-Za-z]+)', column_number)
        if match:
            return match.group(1).upper()

    def excel_column_to_number(self, column_number)->int:
        num = 0
        for char in column_number.upper():
            num = num * 26 + (ord(char) - ord('A') + 1)
        return num
    
    def number_to_excel_format(self,n):
        result = ""
        while n > 0:
            n, remainder = divmod(n - 1, 26)
            result = chr(65 + remainder) + result
        return result + "1"

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






