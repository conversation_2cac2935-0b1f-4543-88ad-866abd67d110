from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB删除行 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('record_row','RecordRow')

    def execute(self):
        record_row = self.get_arg('record_row')
        browser_selector = self.get_arg('browser_selector')
        self.delete_view_row(browser_selector,record_row)

    def delete_view_row(self,browser_selector,record_row):
        js = f"window.WPSOpenApi.Application.ActiveDBSheet.View.DeleteViewRow({record_row});"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






