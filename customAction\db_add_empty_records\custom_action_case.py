from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB新增空白记录 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('record_number','RecordNumber') 

        
    def execute(self):
        record_number = self.get_arg('record_number')
        browser_selector = self.get_arg('browser_selector')
        self.add_empty_records(browser_selector,record_number)

    def add_empty_records(self,browser_selector,record_number):
        js = f"window.WPSOpenApi.Application.ActiveDBSheet.View.InsertRecord({record_number})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






