from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    AP区选区域 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('begin','Begin')
        self.add_required_arg('end','End')

        
    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        begin = self.get_arg('begin')
        end = self.get_arg('end')
        self.selection_set_range(browser_selector,begin,end)

    def selection_set_range(self,browser_selector,begin,end):
        js = f"async()=>await window.WPSOpenApi.Application.ActiveOutline.Editor.Document.Selection.SetSelection({begin},{end});"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






