from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    ET切换工作表 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('sheet_index','SheetIndex')

    def execute(self):
        sheet_index = self.get_arg('sheet_index')
        browser_selector = self.get_arg('browser_selector')
        self.et_select_sheet(browser_selector,sheet_index)

    def et_select_sheet(self,browser_selector,sheet_index):
        js = f"window.APP.getActiveBook().getWorksheet({sheet_index}).activate()"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






