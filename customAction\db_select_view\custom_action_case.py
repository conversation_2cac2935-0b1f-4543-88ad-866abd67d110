from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB切换视图 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('view_index','ViewIndex')

    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        view_index = self.get_arg('view_index')
        self.select_view(browser_selector,view_index)

    def select_view(self,browser_selector,view_index):
        js = f"window.APP.OpenAPI.setActiveView({view_index})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






