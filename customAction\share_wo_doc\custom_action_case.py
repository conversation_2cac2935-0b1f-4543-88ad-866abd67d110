from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger

class CustomAction(BaseAction):
    """
    分享云文档
    """
    def parse_args(self, args):
        self.add_required_arg('file_id', 'FileId') # 文件id
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie
        self.add_required_arg('permission', 'Permission')  # 权限，可选值：['write', 'read', 'comment']
        self.add_required_arg('range', 'Range')  # 分享人群，可选值：['anyone', 'company', 'group']

    def execute(self) -> int:
        file_id = self.get_arg('file_id')
        cookies = self.get_arg('cookies')
        permission = self.get_arg('permission')
        range = self.get_arg('range')
        
        # 转换cookies
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        data = {
            'fileid': file_id,
            'permission': permission,
            'range': range,
            'csrfmiddlewaretoken': cookies_dict['csrf'],
            "period": 0
        }
        
        url = 'https://drive.kdocs.cn/api/v5/links'
        res = requests.post(url, json=data, cookies=cookies_dict, verify=False)
        if res.status_code != 200:
            logger.error(f'分享云文档失败：{res.text}')
            raise Exception(f'分享云文档失败：{res.text}')
        logger.info(f'分享云文档结果：{res.json()}')
        
        # 输出变量
        link_url = res.json()['linkinfo']['link_url']
        self.set_step_variable('link_url', link_url)
        
        return link_url
        

if __name__ == '__main__':
    pass
