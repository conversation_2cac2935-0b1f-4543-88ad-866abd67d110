from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB选中列 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('column_number','ColumnNumber')

    def execute(self):

        column_number = self.get_arg('column_number')
        browser_selector = self.get_arg('browser_selector')
        self.select_column(browser_selector,column_number)

    def select_column(self,browser_selector,column_number):
        js = f"async()=>await window.WPSOpenApi.Application.ActiveDBSheet.Selection.SelectCol({column_number});"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






