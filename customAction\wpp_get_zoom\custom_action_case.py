from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    WPP获取显示比例 自定义动作
    """

   
    
    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')

    def execute(self) -> float:
        browser_selector = self.get_arg('browser_selector')
        
        zoom = self.execute_script(browser_selector,f"window.APP.shell.doc.getZoom()")
        actual_decimal = Decimal(zoom['actual'])
        decimal_zoom = actual_decimal.quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        formatted_zoom = float(decimal_zoom)
        self.set_step_variable('zoom',formatted_zoom)
        return formatted_zoom

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






