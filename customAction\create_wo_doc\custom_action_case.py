from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger
import uuid

class CustomAction(BaseAction):
    """
    创建云文档
    """
    def parse_args(self, args):
        self.add_required_arg('component', 'Component') # 组件类型
        self.add_required_arg('gid', 'GroupId') # 分组ID
        self.add_required_arg('pid', 'ParentId') # 父文件夹ID
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie
        self.add_arg('file_name', 'FileName', '') # 文件名称

    def execute(self) -> int:
        component = self.get_arg('component')
        gid = self.get_arg('gid')
        pid = self.get_arg('pid')
        cookies = self.get_arg('cookies')
        file_name = self.get_arg('file_name')
        
        file_type = {
            'wps' : 'docx',
            'et' : 'xlsx',
            'wpp' : 'pptx',
            # 'pdf' : 'pdf', 目前云文档不支持新建pdf
            'whiteboard' : 'kw',
            'flexpaper' : 'otl',
            'dbsheet': 'dbt',
            'ksheet': 'ksheet',
        }
        if component.lower() not in file_type:
            logger.error(f'不支持的组件类型: {component}')
            raise Exception(f'不支持的组件类型: {component}')
        
        file_name = file_name if file_name else f'{str(uuid.uuid1())}-base'
        file_name = f'{file_name}.{file_type[component.lower()]}'
        json_params = {
            'type': component,
            'platform': 8,
            'from': 'wps_plus',
            'gid': int(gid),
            'pid': int(pid),
            'name': file_name
        }
        # 转换cookies
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        res = requests.post(url='https://docer-api.kdocs.cn/newdocs/u/v1/emptydocs/create',
                            json=json_params,
                            cookies=cookies_dict,
                            verify=False
                            )
        if res.status_code != 200:
            logger.error(f'创建云文档失败: {res.text}')
            raise Exception(f'创建云文档失败: {res.text}')
        logger.info(f'创建云文档成功: {res.json()}')
        
        # 输出变量
        self.set_step_variable('id', res.json()['data']['id'])
        self.set_step_variable('file_name', res.json()['data']['file_name'])
        self.set_step_variable('link_id', res.json()['data']['link_id'])
        self.set_step_variable('link_url', res.json()['data']['link_url'])
        self.set_step_variable('branch_id', res.json()['data']['branch_id'])
        
        return res.json()['data']



if __name__ == '__main__':
    pass
