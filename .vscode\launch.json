{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "CustomActionDebug",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/case.py",
            "args": ["${workspaceFolder}/case_conf.json"],
            "justMyCode": false,
            "subProcess": true,
            "console": "integratedTerminal",
            "purpose": ["debug-in-terminal"],  // 支持多进程
            "env": {
                "PYTHONPATH": "${workspaceFolder};${workspaceFolder}/customAction"
            },
            "stopOnEntry": false,
            "showReturnValue": true,
            "redirectOutput": true,
        }
    ]
}