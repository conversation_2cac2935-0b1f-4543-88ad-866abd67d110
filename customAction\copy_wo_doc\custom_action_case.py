from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger
import uuid
import json


class CustomAction(BaseAction):
    """
    复制云文档
    """
    def parse_args(self, args):
        self.add_required_arg('source_id', 'SourceId')  # 源文档ID
        self.add_required_arg('gid', 'GroupId')  # 目标分组ID
        self.add_required_arg('pid', 'ParentId')  # 目标父文件夹ID
        self.add_required_arg('cookies', 'Cookies') # 浏览器Cookie
        self.add_required_arg('fname', 'FileName')  # 文件名称
        self.add_arg('branch', 'Branch', '')  # 分支名称，可选参数

    def execute(self) -> int:
        source_id = self.get_arg('source_id')
        gid = self.get_arg('gid')
        pid = self.get_arg('pid')
        fname = self.get_arg('fname')
        branch = self.get_arg('branch')
        cookies = self.get_arg('cookies')
        # 转换cookies
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        # 从cookies_dict中获取wps_sid
        wps_sid = cookies_dict['wps_sid']
        if not wps_sid:
            logger.error('wps_sid不存在')
        # 从cookies_dict中获取csrf
        csrf_token = cookies_dict['csrf']
        if not csrf_token:
            logger.error('csrf不存在')
            
        # 调用复制文档函数
        file_detail = self.docs_save_as(
            id=source_id,
            fname=fname,
            target_groupid=gid,
            target_parentid=pid,
            csrf_token=csrf_token,
            WPS_SID=wps_sid,
            BRANCH=branch
        )
        
        if not file_detail:
            logger.error('复制云文档失败')
            return 1
        
        
        # 输出变量
        self.set_step_variable('id', file_detail['fileid'])
        self.set_step_variable('link_id', file_detail['linkid'])
        self.set_step_variable('link_url', file_detail['linkurl'])
        
        logger.info(f'复制云文档成功: {file_detail["fileid"]}')
        return 0

    def docs_save_as(self, *, id, fname, target_groupid, target_parentid, csrf_token, WPS_SID, BRANCH):
        """
        文档另存为
        """
        headers = {
            "Cookie": f"wps_sid={WPS_SID};csrf={csrf_token};weboffice_branch={BRANCH}",
            "Origin": "https://www.kdocs.cn"
        }
        
        file_info_url = f'https://www.kdocs.cn/api/kdocs/sharefile/{id}/info'
        file_info_req = requests.get(file_info_url,headers=headers, verify=False)
        logger.info(f"文档另存为原文件信息: {file_info_req.text}")
        file_type = (json.loads(file_info_req.text)['data']['name']).split('.')[-1]

        save_as_url = f'https://drive.kdocs.cn/api/v5/files/{id}/save_as'
        file_name = f'{str(uuid.uuid1())}-{fname}.{file_type}'
        
        logger.info(f"开始另存为文档: {id} -> {file_name}")
        
        save_as_req = requests.post(save_as_url, json={
            "fname": file_name,
            "fileids": str(id),
            "target_groupid": int(target_groupid),
            "target_parentid": int(target_parentid),
            "csrfmiddlewaretoken": csrf_token
        }, headers=headers, verify=False)
        
        if save_as_req.status_code == 200 and save_as_req.json().get('fileid'):
            logger.info(f"另存为文档成功: {save_as_req.json()}")
            return save_as_req.json()
        else:
            logger.error(f"另存为文档接口请求失败: {save_as_req.text}")
            return None
    


if __name__ == '__main__':
    pass
