from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    AP定位到正文区结束位置 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')

        
    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        self.selection_set_end(browser_selector)

    def selection_set_end(self,browser_selector):
        js = f"""
        var end = window.APP.OTL.view.state.doc.content.size - 3;
        (async()=>{{
            await window.WPSOpenApi.ready(); 
            await window.WPSOpenApi.Application.ActiveOutline.Editor.Document.Selection.SetSelection(end,end);
        }})();
        """
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






