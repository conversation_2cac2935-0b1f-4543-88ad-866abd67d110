from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    WPP设置页面缩放比 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('zoom', 'Zoom')


    def execute(self):

        zoom = self.get_arg('zoom')
        browser_selector = self.get_arg('browser_selector')
        self.wpp_set_zoom(browser_selector,zoom)

    def wpp_set_zoom(self,browser_selector,zoom):
        js = f"APP.shell.doc.setZoom({zoom})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






