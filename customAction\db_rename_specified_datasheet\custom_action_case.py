from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    DB重命名指定数据表 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('datasheet_index','DatasheetIndex')
        self.add_required_arg('datasheet_name','DatasheetName')

    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        datasheet_index = self.get_arg('datasheet_index')
        datasheet_name = self.get_arg('datasheet_name')
        self.rename_view(browser_selector,datasheet_index,datasheet_name)

    def rename_view(self,browser_selector,datasheet_index,datasheet_name):
        js = f"window.APP.OpenAPI.renameSheet({datasheet_index}, '{datasheet_name}')"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






