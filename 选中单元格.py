from weboffice_v2.pc.component.actions.base import BaseAction
import requests
from eastwind.utils.io.log import logger



class CustomAction(BaseAction):
    """
    选中单元格
    """

    def parse_args(self, args):
        self.add_required_arg('project', 'Project')  # 组件
        self.add_required_arg('range', 'Range')  # 选中单元格范围
        self.add_required_arg('row', 'Row')  # DB行
        self.add_required_arg('column', 'Column')  # DB列
        self.add_required_arg('browser_selector', 'BrowserSelector')  #浏览器对象选择器

    def execute(self) :
        project = self.get_arg('project')

        range = self.get_arg('range')
        row = self.get_arg('row')
        column = self.get_arg('column')
        browser_selector= self.get_arg('browser_selector')

        self.Select_cell(browser_selector,range,project,row,column)


    def Select_cell(self, browser_selector,range,project,row,column):
        if project=="ET" or project=="AS":
            js = f"window.WPSOpenApi.Application.Range('{range}').Activate()"
            self.execute_script(browser_selector, js)
        if project=="DB":
            js = f"window.WPSOpenApi.Application.ActiveDBSheet.Selection.SelectCell({row}, {column})"
            self.execute_script(browser_selector, js)


    def execute_script(self, browser_selector, script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script,
        }
        return self.driver_context.execute_action('EvaluateJS', args=args)

