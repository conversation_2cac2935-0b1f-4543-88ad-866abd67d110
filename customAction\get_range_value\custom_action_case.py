from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    检查单元格数据 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('location', 'Location')
        self.add_required_arg('range_type', 'RangeType')

    def execute(self) -> str:
        browser_selector = self.get_arg('browser_selector')
        location = self.get_arg('location')
        range_type = self.get_arg('range_type')
        return self.get_range_value(browser_selector,location,range_type)

    def get_range_value(self,browser_selector,location,range_type) -> str:
        js = ""
        if range_type == 'ET' or range_type == 'AS':
            js = f"window.APP.getActiveSheet().getCellString({location})"
        elif range_type == 'DB':
            js = f"window.APP.getActiveBook().getActiveSheet().getActiveDbSheetView().getCellInfo({location}).getCellString()"
        return self.execute_script(browser_selector,js)['actual']

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






