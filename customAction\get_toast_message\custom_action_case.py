from eastwind.utils.io.log import logger
from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    获取toast消息 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')

    def execute(self):
        browser_selector = self.get_arg('browser_selector')
        script = f"""
        async()=>await window.WPSOpenApi.commonApiReady();
        const hook = async()=>await window.WPSOpenApi.CommonApi.Hooks.Add('toast')
        hook.OnAction = (e) =>{{
            window.e = e;
            console.error("===========",window.e);
            window.e;
            }}
        """
        result =  self.execute_script(browser_selector, script)['actual']
        logger.info(f"result==========> {result}")
        result_json = result.json()
        logger.info(f"result_json==========> {result_json}")

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






