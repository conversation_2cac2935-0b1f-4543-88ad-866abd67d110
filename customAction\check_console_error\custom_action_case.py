from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests
from eastwind.utils.io.log import logger
from decimal import Decimal, ROUND_HALF_UP


class CustomAction(BaseCheckpoint):
    """
    检查控制台无报错 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')

    def execute(self) -> int:
        browser_selector = self.get_arg('browser_selector')
        result = self.check_console_error(browser_selector)
        
        # 是否有错误信息
        if hasattr(self, 'error_message'):
            error_msg = self.error_message
            delattr(self, 'error_message')  # 清除错误信息
            raise Exception(f"控制台错误信息: {error_msg}")    
        return result
            
    def check_console_error(self,browser_selector) -> int:
        
        length_js = f'window.errorRecords.length'
        error_length = self.execute_script(browser_selector,length_js)['actual']
        error_length = int(error_length)
        
        js = f"window.errorRecords"
        error_detail = self.execute_script(browser_selector,js)['actual']
        if error_length > 0:
            error_msg = ""
            # 字符串形式的 JSON
            if isinstance(error_detail, str) and error_detail.startswith('[') and error_detail.endswith(']'):
                content = error_detail[1:-1]
                error_msg = content
            # 如果是 Python 对象
            elif isinstance(error_detail, list) and len(error_detail) > 0:
                first_item = error_detail[0]
                if isinstance(first_item, dict):
                    # 字典转换为字符串形式
                    error_msg = ", ".join([f"{key}: {value}" for key, value in first_item.items()])
                else:
                    error_msg = str(first_item)
            else:
                error_msg = str(error_detail)
            
            self.error_message = error_msg
            
        return error_length
        
    def __del__(self):
        # 移除 __del__ 方法，不是一个可靠的方式来抛出异常
        pass

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)
    


if __name__ == '__main__':
    pass






