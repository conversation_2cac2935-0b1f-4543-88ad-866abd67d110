import json
import time

import requests
from eastwind.utils.io.log import logger
from extend_driver.core.os.windows.utils.cef import CefUtil
from weboffice_v2.pc.component.actions.base import BaseAction

GET_WS_URL = "http://localhost:{}/json/version"


class CustomAction(BaseAction):
    """
    连接CEF浏览器
    """

    def parse_args(self, args):
        self.add_required_arg('cef_container_type', 'CefContainerType')
        self.add_required_arg('custom_type_name', 'CustomTypeName')
        self.add_arg('cef_port_index', 'CefPortIndex', 0)

    def execute(self) -> int:
        cef_container_type = self.get_arg('cef_container_type')
        custom_type_name = self.get_arg('custom_type_name')
        cef_port_index = self.get_arg('cef_port_index')

        # 查找CEF端口
        cef_port = CefUtil.get_cef_port(cef_container_type, custom_type_name, cef_port_index)
        if not cef_port:
            raise Exception('未找到CEF端口')
        logger.info(f'获取到CEF端口：{cef_port}')

        # 查询WS调试连接
        debug_url = self.get_ws_url(cef_port)
        if not debug_url:
            raise Exception('未找到WS调试连接')
        logger.info(f'获取到WS调试连接：{debug_url}')

        # 返回CEF调试URL
        self.set_step_variable("debug_url", debug_url)

        return 0

    def get_ws_url(self, port, retry_times=10):
        for _ in range(retry_times):
            ws_response = json.loads(requests.get(url=GET_WS_URL.format(port), timeout=10).text)

            if not ws_response or not ws_response.get('webSocketDebuggerUrl'):
                time.sleep(1)
                continue

            return ws_response.get('webSocketDebuggerUrl')

        raise Exception('获取webSocketDebuggerUrl失败')


if __name__ == '__main__':
    pass
