from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    WPP切换至指定页 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector', 'BrowserSelector')
        self.add_required_arg('page_number', 'pageNumber')


    def execute(self):

        page_number = self.get_arg('page_number')
        browser_selector = self.get_arg('browser_selector')
        self.set_active_slide(browser_selector,page_number)

    def set_active_slide(self,browser_selector,page_number):
        js = f"APP.shell.doc.setActiveSlide({page_number})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






