from weboffice_v2.pc.component.actions.base import BaseAction
import requests


class CustomAction(BaseAction):
    """
    WPP选中表格的单元格 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('start_row', 'StartRow')
        self.add_required_arg('start_col', 'StartCol')
        self.add_required_arg('end_row', 'EndRow')
        self.add_required_arg('end_col', 'EndCol')
        self.add_required_arg('browser_selector','BrowserSelector')

    def execute(self):

        start_row = self.get_arg('start_row')
        start_col = self.get_arg('start_col')
        end_row = self.get_arg('end_row')
        end_col = self.get_arg('end_col')
        browser_selector = self.get_arg('browser_selector')
        self.get_object_id(browser_selector,start_row,start_col,end_row,end_col)

    def get_object_id(self,browser_selector,start_row,start_col,end_row,end_col):
        js = f"window.APP.shell.doc.getLocalSelection().selectTableCells({start_row},{start_col},{end_row},{end_col})"
        self.execute_script(browser_selector,js)

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






