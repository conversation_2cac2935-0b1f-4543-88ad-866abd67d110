from weboffice_v2.pc.component.checkpoints.base import BaseCheckpoint
import requests

class CustomAction(BaseCheckpoint):
    """
    AP获取当前选区位置 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')

        
    def execute(self)->str:
        browser_selector = self.get_arg('browser_selector')
        return self.get_current_selection(browser_selector)

    def get_current_selection(self,browser_selector)->str:
        js_begin = f"APP.OTL.selection.begin"
        js_end = f"APP.OTL.selection.end"
        begin = self.execute_script(browser_selector,js_begin)['actual']
        end = self.execute_script(browser_selector,js_end)['actual']
        return f"{begin},{end}"

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






